from binance.client import Client
from binance.exceptions import BinanceAPIException, BinanceOrderException

# --- 配置 API ---
# 请替换为您的真实 API Key 和 Secret
API_KEY = "Gz1gnubIZyMJYz3cb47vlrIZp6NUfnhhVr58Kr9ztyD3ypaVDNbv5UuLYa2NY9nZ"
API_SECRET = "4nTzQNOt8cNM3mBFMOcQ5Qf8TDluYDH5vn3DMN5YxqROoLuuy9Fynnt5VYdrr9QK"

# --- 初始化客户端 ---
# 如果您在中国大陆，并且遇到连接问题，可以尝试设置 tld='cn'
# client = Client(API_KEY, API_SECRET, tld='cn')
# 对于国际用户或使用VPN的用户：
client = Client(API_KEY, API_SECRET)

# --- 合约交易参数 ---
SYMBOL = 'BTCUSDC'

def place_futures_market_order(symbol, side, quantity):
    """
    下市价单
    :param symbol: 交易对, e.g., 'BTCUSDT'
    :param side: 'BUY' 或 'SELL'
    :param quantity: 数量 (BTC的数量)
    :return: 订单信息或错误信息
    """
    try:
        print(f"正在尝试下市价单: {side} {quantity} {symbol}")
        order = client.futures_create_order(
            symbol=symbol,
            side=side,
            type=Client.ORDER_TYPE_MARKET,
            quantity=quantity
        )
        print("市价单下单成功:")
        print(order)
        return order
    except BinanceAPIException as e:
        print(f"币安 API 错误: {e}")
        return None
    except BinanceOrderException as e:
        print(f"币安订单错误: {e}")
        return None
    except Exception as e:
        print(f"发生未知错误: {e}")
        return None

def place_futures_limit_order(symbol, side, quantity, price, position_side='BOTH', time_in_force='GTC'):
    """
    下限价单
    :param symbol: 交易对, e.g., 'BTCUSDT'
    :param side: 'BUY' 或 'SELL'
    :param quantity: 数量 (BTC的数量)
    :param price: 价格
    :param time_in_force: 有效方式, e.g., 'GTC' (Good Til Canceled), 'IOC', 'FOK'
    :return: 订单信息或错误信息
    """
    try:
        print(f"正在尝试下限价单: {side} {quantity} {symbol} @ {price} (持仓方向: {position_side})")
        order = client.futures_create_order(
            symbol=symbol,
            side=side,
            type=Client.ORDER_TYPE_LIMIT,
            timeInForce=time_in_force,
            quantity=quantity,
            price=str(price),  # 价格需要是字符串格式
            positionSide=position_side  # 指定持仓方向
        )
        print("限价单下单成功:")
        print(order)
        return order
    except BinanceAPIException as e:
        print(f"币安 API 错误: {e}")
        return None
    except BinanceOrderException as e:
        print(f"币安订单错误: {e}")
        return None
    except Exception as e:
        print(f"发生未知错误: {e}")
        return None

def cancel_futures_order(symbol, order_id):
    """
    取消指定合约订单
    :param symbol: 交易对, e.g., 'BTCUSDT'
    :param order_id: 要取消的订单ID
    :return: 取消结果或错误信息
    """
    try:
        print(f"正在尝试取消订单: {symbol} 订单ID {order_id}")
        result = client.futures_cancel_order(
            symbol=symbol,
            orderId=order_id
        )
        print("订单取消成功:")
        print(result)
        return result
    except BinanceAPIException as e:
        print(f"币安 API 错误: {e}")
        return None
    except BinanceOrderException as e:
        print(f"币安订单错误: {e}")
        return None
    except Exception as e:
        print(f"发生未知错误: {e}")
        return None


if __name__ == '__main__':
    # --- 检查账户状态 (可选) ---
    try:
        account_info = client.futures_account()
        print("合约账户信息获取成功。")
        # 获取当前持仓模式
        position_mode = client.futures_get_position_mode()
        print(f"当前持仓模式: {position_mode}")
        # print(account_info) # 可以取消注释查看详细账户信息
    except BinanceAPIException as e:
        print(f"无法获取合约账户信息，请检查API密钥权限和网络连接: {e}")
        exit() # 如果无法连接或密钥无效，则退出

    # --- 下单示例 ---
    # **重要提示: 以下下单操作会真实执行，请务必小心并使用小额资金测试**
    # **在实际使用前，请确保您了解市价单和限价单的区别以及相关风险**

    # 示例1: 下一个市价买单，购买 0.001 BTC
    # order_market_buy = place_futures_market_order(SYMBOL, Client.SIDE_BUY, 0.001)
    # if order_market_buy:
    #     print(f"市价买单 {order_market_buy['orderId']} 已提交。")

    # 示例2: 下一个市价卖单，卖出 0.001 BTC
    # order_market_sell = place_futures_market_order(SYMBOL, Client.SIDE_SELL, 0.001)
    # if order_market_sell:
    #     print(f"市价卖单 {order_market_sell['orderId']} 已提交。")

    # 示例3: 下一个限价买单，假设当前BTC价格为20000USDT，我们想在19500USDT购买0.001 BTC
    # current_btc_price = 20000 # 假设的价格，实际应从API获取或自行判断
    # limit_buy_price = current_btc_price * 0.98 # 假设在当前价格的98%处挂买单
    # order_limit_buy = place_futures_limit_order(SYMBOL, Client.SIDE_BUY, 0.001, round(limit_buy_price, 2))
    # if order_limit_buy:
    #     print(f"限价买单 {order_limit_buy['orderId']} 已提交。")

    # 示例4: 下一个限价卖单，假设当前BTC价格为20000USDT，我们想在20500USDT卖出0.001 BTC
    # current_btc_price = 20000 # 假设的价格
    # limit_sell_price = current_btc_price * 1.02 # 假设在当前价格的102%处挂卖单
    # order_limit_sell = place_futures_limit_order(SYMBOL, Client.SIDE_SELL, 0.001, round(limit_sell_price, 2))
    # if order_limit_sell:
    #     print(f"限价卖单 {order_6limit_sell['orderId']} 已提交。")

    # # 对于卖出订单，根据您的交易策略选择适当的持仓方向
    # current_btc_price = 109514.7
    #
    # # 如果您想开空仓位
    # order_limit_sell = place_futures_limit_order(
    #     SYMBOL,
    #     Client.SIDE_SELL,
    #     0.001,
    #     round(current_btc_price, 2),
    #     position_side='SHORT'  # 指定为空头持仓
    # )
    # if order_limit_sell:
    #     print(f"限价买单 {order_limit_sell['orderId']} 已提交。")


    test_order_id = '16233377094'
    if test_order_id:
        # 取消指定订单
        cancel_result = cancel_futures_order(SYMBOL, test_order_id)
        if cancel_result:
            print(f"订单 {cancel_result['orderId']} 已成功取消。")


    print("\n--- 示例结束 ---")
    print("请取消注释并修改 `if __name__ == '__main__':` 代码块中的下单示例以进行实际操作。")
    print("务必小心操作，真实交易涉及资金风险。")
